package com.example.myapp.repository

import com.example.myapp.data.Project
import com.example.myapp.data.ProjectCategory

class ProjectRepository {
    
    fun getAllProjects(): List<Project> {
        return listOf(
            Project(
                id = 1,
                title = "Fitbit",
                description = "A mobile application for tracking workouts and health metrics",
                imageUrl = "https://akash-kanabur.netlify.app/fitbit(2).png",
                githubUrl = "https://github.com/akashkanabur/FitBit?tab=readme-ov-file",
                videoUrl = "https://akash-kanabur.netlify.app/fitbit.mp4",
                technologies = listOf("Flutter", "Firebase", "MVVM"),
                category = ProjectCategory.APP_DEVELOPMENT
            ),
            Project(
                id = 2,
                title = "Signature Forgery Detection",
                description = "AI App for Signature Verification",
                imageUrl = "https://akash-kanabur.netlify.app/Signature.png",
                githubUrl = "https://github.com/akashkanabur/SignatureDetection",
                videoUrl = "https://akash-kanabur.netlify.app/SignDetect.mp4",
                technologies = listOf("React Native", "Node.js", "MongoDB"),
                category = ProjectCategory.APP_DEVELOPMENT
            ),
            Project(
                id = 3,
                title = "Harmony Harvest",
                description = "To prevent food waste",
                imageUrl = "",
                githubUrl = "https://github.com/akashkanabur/HarmonyHarvest",
                technologies = listOf("Kotlin", "Firebase", "Python"),
                category = ProjectCategory.APP_DEVELOPMENT
            ),
            Project(
                id = 4,
                title = "ConvoCraft",
                description = "Chat application with AI trained bot",
                imageUrl = "",
                githubUrl = "https://github.com/akashkanabur/ConvoCraft",
                technologies = listOf("Kotlin", "DBMS", "MVVM", "Firebase"),
                category = ProjectCategory.APP_DEVELOPMENT
            ),
            Project(
                id = 5,
                title = "DeepFake Audio App",
                description = "Real-time weather forecasts with location-based alerts",
                imageUrl = "",
                githubUrl = "https://github.com/akashkanabur/DeepfakeAudioApp",
                technologies = listOf("Flutter", "Weather API"),
                category = ProjectCategory.APP_DEVELOPMENT
            ),
            Project(
                id = 6,
                title = "EchoNotes",
                description = "Video Summarisation App to generate caption and highlights",
                imageUrl = "https://akash-kanabur.netlify.app/echonotes.png",
                githubUrl = "https://github.com/akashkanabur/EchoNotes",
                technologies = listOf("Kotlin", "XML", "Firebase", "NSTM Model"),
                category = ProjectCategory.APP_DEVELOPMENT
            ),
            Project(
                id = 7,
                title = "WireFrameToCode",
                description = "With the help of AI trained model generate code for the wireframe provided",
                imageUrl = "",
                githubUrl = "https://github.com/akashkanabur/WireFrametoCode",
                technologies = listOf("Kotlin", "Firebase", "Cloud Functions", "XML"),
                category = ProjectCategory.APP_DEVELOPMENT
            ),
            Project(
                id = 8,
                title = "Hackathon Hunt",
                description = "Website to find best hackathons globally",
                imageUrl = "https://akash-kanabur.netlify.app/HackathonHunt.png",
                githubUrl = "https://github.com/akashkanabur/HackathonHunt",
                liveUrl = "https://hack-hunt.netlify.app/",
                technologies = listOf("HTML", "CSS", "JavaScript", "Bootstrap"),
                category = ProjectCategory.WEB_DEVELOPMENT
            ),
            Project(
                id = 9,
                title = "Medical App",
                description = "A modern UI/UX redesign for a banking application",
                imageUrl = "https://akash-kanabur.netlify.app/design3.jpeg",
                liveUrl = "https://www.behance.net/akash26k/projects",
                technologies = listOf("Figma", "Prototyping"),
                category = ProjectCategory.UI_UX_DESIGN
            ),
            Project(
                id = 10,
                title = "Productivity App",
                description = "Intuitive interface for booking flights, hotels, and experiences",
                imageUrl = "https://akash-kanabur.netlify.app/design7.jpeg",
                liveUrl = "https://www.behance.net/akash26k/projects",
                technologies = listOf("Figma", "Miro"),
                category = ProjectCategory.UI_UX_DESIGN
            ),
            Project(
                id = 11,
                title = "Vault Of Secrets",
                description = "Adventurous game inspired from backrooms using UE5 Engine.",
                imageUrl = "https://akash-kanabur.netlify.app/vaultofsecrets.png",
                videoUrl = "https://akash-kanabur.netlify.app/VaultOfSecrets.mp4",
                technologies = listOf("UE5", "Blueprints"),
                category = ProjectCategory.GAME_DEVELOPMENT
            ),
            Project(
                id = 12,
                title = "World of Secrets",
                description = "Game based on the theme Defend your fort",
                imageUrl = "https://akash-kanabur.netlify.app/worldofsecrets.png",
                videoUrl = "https://akash-kanabur.netlify.app/GameDev1.mp4",
                technologies = listOf("UE5", "Blueprints"),
                category = ProjectCategory.GAME_DEVELOPMENT
            ),
            Project(
                id = 13,
                title = "25th Hour",
                description = "Game based on the theme Lost in time",
                imageUrl = "https://akash-kanabur.netlify.app/lostintime.png",
                liveUrl = "https://youtu.be/u5mftgZ56WY",
                technologies = listOf("UE5", "Blueprints"),
                category = ProjectCategory.GAME_DEVELOPMENT
            )
        )
    }
    
    fun getProjectsByCategory(category: ProjectCategory): List<Project> {
        return if (category == ProjectCategory.ALL) {
            getAllProjects()
        } else {
            getAllProjects().filter { it.category == category }
        }
    }
}
