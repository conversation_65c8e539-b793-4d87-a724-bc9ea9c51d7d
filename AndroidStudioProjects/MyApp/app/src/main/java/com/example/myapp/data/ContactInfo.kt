package com.example.myapp.data

data class ContactInfo(
    val email: String = "<EMAIL>",
    val phone: String = "+91 8296389428",
    val location: String = "Bengaluru, India",
    val responseTime: String = "Within 24 hours",
    val behanceUrl: String = "https://www.behance.net/akash26k/projects",
    val devfolioUrl: String = "https://devfolio.co/@akash26504"
)

data class ContactMessage(
    val fullName: String,
    val email: String,
    val subject: String,
    val projectType: String,
    val budgetRange: String,
    val projectDetails: String
)

enum class ProjectType(val displayName: String) {
    MOBILE_APP("Mobile App Development"),
    UI_UX_DESIGN("UI/UX Design"),
    WEB_DESIGN("Web Design"),
    DESIGN_CONSULTATION("Design Consultation"),
    OTHER("Other")
}

enum class BudgetRange(val displayName: String) {
    UNDER_1000("Under $1,000"),
    RANGE_1000_5000("$1,000 - $5,000"),
    RANGE_5000_10000("$5,000 - $10,000"),
    ABOVE_10000("$10,000+"),
    LETS_DISCUSS("Let's discuss")
}
