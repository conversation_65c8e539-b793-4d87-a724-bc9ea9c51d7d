package com.example.myapp.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.myapp.ui.screens.AboutScreen
import com.example.myapp.ui.screens.ContactScreen
import com.example.myapp.ui.screens.HackathonsScreen
import com.example.myapp.ui.screens.HomeScreen
import com.example.myapp.ui.screens.ProjectsScreen

@Composable
fun PortfolioNavigation(navController: NavHostController) {
    NavHost(
        navController = navController,
        startDestination = Screen.Home.route
    ) {
        composable(Screen.Home.route) {
            HomeScreen(navController = navController)
        }
        composable(Screen.About.route) {
            AboutScreen(navController = navController)
        }
        composable(Screen.Projects.route) {
            ProjectsScreen(navController = navController)
        }
        composable(Screen.Contact.route) {
            ContactScreen(navController = navController)
        }
        composable(Screen.Hackathons.route) {
            HackathonsScreen(navController = navController)
        }
    }
}

sealed class Screen(val route: String, val title: String) {
    object Home : Screen("home", "Home")
    object About : Screen("about", "About")
    object Projects : Screen("projects", "Projects")
    object Contact : Screen("contact", "Contact")
    object Hackathons : Screen("hackathons", "Hackathons")
}
