package com.example.myapp.ui.screens

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.myapp.data.BudgetRange
import com.example.myapp.data.ContactInfo
import com.example.myapp.data.ProjectType
import com.example.myapp.ui.theme.AccentBlue
import com.example.myapp.ui.theme.AccentGreen

@Composable
fun ContactScreen(navController: NavController) {
    val context = LocalContext.current
    val contactInfo = remember { ContactInfo() }
    
    // Form state
    var fullName by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var subject by remember { mutableStateOf("") }
    var selectedProjectType by remember { mutableStateOf(ProjectType.MOBILE_APP) }
    var selectedBudgetRange by remember { mutableStateOf(BudgetRange.LETS_DISCUSS) }
    var projectDetails by remember { mutableStateOf("") }
    var isProjectTypeExpanded by remember { mutableStateOf(false) }
    var isBudgetRangeExpanded by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .background(MaterialTheme.colorScheme.background)
    ) {
        // Header
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            AccentBlue.copy(alpha = 0.2f),
                            Color.Transparent
                        )
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Let's Work Together",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onBackground
                )
                Text(
                    text = "Ready to bring your ideas to life? I'd love to hear about your project.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }
        }
        
        // Contact Form
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "Send me a message",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Full Name
                OutlinedTextField(
                    value = fullName,
                    onValueChange = { fullName = it },
                    label = { Text("Full Name *") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Email
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    label = { Text("Email Address *") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Subject
                OutlinedTextField(
                    value = subject,
                    onValueChange = { subject = it },
                    label = { Text("Subject *") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Project Type Dropdown
                ExposedDropdownMenuBox(
                    expanded = isProjectTypeExpanded,
                    onExpandedChange = { isProjectTypeExpanded = !isProjectTypeExpanded }
                ) {
                    OutlinedTextField(
                        value = selectedProjectType.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Project Type") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isProjectTypeExpanded) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    ExposedDropdownMenu(
                        expanded = isProjectTypeExpanded,
                        onDismissRequest = { isProjectTypeExpanded = false }
                    ) {
                        ProjectType.values().forEach { projectType ->
                            DropdownMenuItem(
                                text = { Text(projectType.displayName) },
                                onClick = {
                                    selectedProjectType = projectType
                                    isProjectTypeExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Budget Range Dropdown
                ExposedDropdownMenuBox(
                    expanded = isBudgetRangeExpanded,
                    onExpandedChange = { isBudgetRangeExpanded = !isBudgetRangeExpanded }
                ) {
                    OutlinedTextField(
                        value = selectedBudgetRange.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Budget Range") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isBudgetRangeExpanded) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    ExposedDropdownMenu(
                        expanded = isBudgetRangeExpanded,
                        onDismissRequest = { isBudgetRangeExpanded = false }
                    ) {
                        BudgetRange.values().forEach { budgetRange ->
                            DropdownMenuItem(
                                text = { Text(budgetRange.displayName) },
                                onClick = {
                                    selectedBudgetRange = budgetRange
                                    isBudgetRangeExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Project Details
                OutlinedTextField(
                    value = projectDetails,
                    onValueChange = { projectDetails = it },
                    label = { Text("Project Details *") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 4,
                    maxLines = 6
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // Send Button
                Button(
                    onClick = {
                        val emailIntent = Intent(Intent.ACTION_SENDTO).apply {
                            data = Uri.parse("mailto:${contactInfo.email}")
                            putExtra(Intent.EXTRA_SUBJECT, subject)
                            putExtra(Intent.EXTRA_TEXT, """
                                Name: $fullName
                                Email: $email
                                Project Type: ${selectedProjectType.displayName}
                                Budget Range: ${selectedBudgetRange.displayName}
                                
                                Project Details:
                                $projectDetails
                            """.trimIndent())
                        }
                        context.startActivity(emailIntent)
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = fullName.isNotBlank() && email.isNotBlank() && subject.isNotBlank() && projectDetails.isNotBlank()
                ) {
                    Text("Send Message")
                }
            }
        }
        
        // Contact Information
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "Get in touch",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "I'm always excited to work on new projects and collaborate with amazing people. Let's create something great together!",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                ContactInfoItem(
                    icon = Icons.Default.Email,
                    title = "Email",
                    value = contactInfo.email,
                    onClick = {
                        val intent = Intent(Intent.ACTION_SENDTO, Uri.parse("mailto:${contactInfo.email}"))
                        context.startActivity(intent)
                    }
                )
                
                ContactInfoItem(
                    icon = Icons.Default.Phone,
                    title = "Phone",
                    value = contactInfo.phone,
                    onClick = {
                        val intent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:${contactInfo.phone}"))
                        context.startActivity(intent)
                    }
                )
                
                ContactInfoItem(
                    icon = Icons.Default.LocationOn,
                    title = "Location",
                    value = contactInfo.location
                )
                
                ContactInfoItem(
                    icon = Icons.Default.Schedule,
                    title = "Response Time",
                    value = contactInfo.responseTime
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactInfoItem(
    icon: ImageVector,
    title: String,
    value: String,
    onClick: (() -> Unit)? = null
) {
    val modifier = if (onClick != null) {
        Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    } else {
        Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    }
    
    Card(
        onClick = onClick ?: { },
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = if (onClick != null) AccentBlue.copy(alpha = 0.05f) else Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = if (onClick != null) 2.dp else 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = AccentBlue,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = value,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}
