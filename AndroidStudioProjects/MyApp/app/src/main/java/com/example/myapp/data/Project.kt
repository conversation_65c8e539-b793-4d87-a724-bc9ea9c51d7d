package com.example.myapp.data

data class Project(
    val id: Int,
    val title: String,
    val description: String,
    val imageUrl: String,
    val githubUrl: String? = null,
    val liveUrl: String? = null,
    val videoUrl: String? = null,
    val technologies: List<String>,
    val category: ProjectCategory
)

enum class ProjectCategory(val displayName: String) {
    APP_DEVELOPMENT("App Development"),
    WEB_DEVELOPMENT("Web Development"),
    UI_UX_DESIGN("UI/UX Design"),
    GAME_DEVELOPMENT("Game Development"),
    ALL("All Projects")
}
